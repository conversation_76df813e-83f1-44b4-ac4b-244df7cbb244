import { useEffect, useState } from 'react';
import './confetti.css';

interface ConfettiAnimationProps {
  isActive: boolean;
  onComplete?: () => void;
  duration?: number;
}

export function ConfettiAnimation({ isActive, onComplete, duration = 4000 }: ConfettiAnimationProps) {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!isActive) return;

    setIsAnimating(true);

    const cleanup = setTimeout(() => {
      setIsAnimating(false);
      onComplete?.();
    }, duration);

    return () => {
      clearTimeout(cleanup);
    };
  }, [isActive, duration, onComplete]);

  if (!isAnimating) return null;

  // Generate confetti pieces
  const confettiPieces = Array.from({ length: 50 }, (_, i) => {
    const delay = Math.random() * 1000;
    const duration = 3000 + Math.random() * 2000;
    const startX = Math.random() * 100;
    const endX = startX + (Math.random() - 0.5) * 100;
    const rotation = Math.random() * 720;
    const scale = 0.5 + Math.random() * 0.8;

    return (
      <div
        key={i}
        className="absolute text-2xl pointer-events-none"
        style={{
          left: `${startX}%`,
          top: '-10%',
          fontSize: `${scale}em`,
          animationDelay: `${delay}ms`,
          animationDuration: `${duration}ms`,
          transform: `rotate(${rotation}deg)`,
        }}
      >
        <div className="animate-confetti-fall">
          {['🎉', '🎊', '⭐', '✨', '🌟', '💫', '🎈'][Math.floor(Math.random() * 7)]}
        </div>
      </div>
    );
  });

  return (
    <div className="fixed inset-0 pointer-events-none z-50 overflow-hidden">
      {confettiPieces}

      {/* Celebration message */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center animate-celebration-bounce">
          <div className="text-8xl mb-4 animate-sparkle">🎉</div>
          <div className="bg-gradient-to-r from-emerald-500 to-green-500 text-white text-3xl font-bold px-8 py-4 rounded-2xl shadow-2xl border-4 border-white animate-pulse-glow">
            <div className="flex items-center gap-3">
              <span className="animate-sparkle">✨</span>
              Project Approved!
              <span className="animate-sparkle">✨</span>
            </div>
          </div>
          <div className="mt-4 text-emerald-600 font-semibold text-lg animate-pulse">
            Congratulations! 🎊
          </div>
        </div>
      </div>

      {/* Background sparkle effect */}
      <div className="absolute inset-0 bg-gradient-to-b from-yellow-100/20 via-transparent to-emerald-100/20 animate-pulse" />
    </div>
  );
}
