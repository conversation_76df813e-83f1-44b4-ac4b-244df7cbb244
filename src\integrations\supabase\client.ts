// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ucgrtnihycklwkhaheky.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVjZ3J0bmloeWNrbHdraGFoZWt5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1MjU5NjYsImV4cCI6MjA2OTEwMTk2Nn0.VaaM_if3cSKzhG_tQpcNmg0OpdDow3AL1Qlq5fUIH7A";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});