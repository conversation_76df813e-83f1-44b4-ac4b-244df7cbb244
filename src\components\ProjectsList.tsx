import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { useToast } from './ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';
import { Loader2, RefreshCw, Edit, RotateCcw } from 'lucide-react';
import { ProjectEditForm } from './ProjectEditForm';

interface Project {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  ai_feedback: string | null;
  created_at: string;
  updated_at: string;
}

interface ProjectsListProps {
  user: User;
  refreshTrigger: number;
}

export function ProjectsList({ user, refreshTrigger }: ProjectsListProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [evaluatingId, setEvaluatingId] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProjects((data || []) as Project[]);
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const triggerEvaluation = async (projectId: string) => {
    setEvaluatingId(projectId);
    try {
      const { data, error } = await supabase.functions.invoke('evaluate-project', {
        body: { projectId }
      });

      if (error) {
        console.error('Supabase function error:', error);
        // If the function fails, try a fallback evaluation
        await fallbackEvaluation(projectId);
        return;
      }

      const evaluationMethod = data.usedFallback ? ' (using basic evaluation)' : ' (using AI evaluation)';
      console.log('Evaluation response:', data);
      toast({
        title: "Evaluation complete!",
        description: `Project has been ${data.newStatus}${evaluationMethod}`,
      });

      await fetchProjects();
    } catch (error: any) {
      console.error('Evaluation error:', error);
      // Try fallback evaluation
      try {
        await fallbackEvaluation(projectId);
      } catch (fallbackError) {
        toast({
          title: "Evaluation Failed",
          description: "AI evaluation service is currently unavailable. Please try again later or contact support.",
          variant: "destructive",
        });
      }
    } finally {
      setEvaluatingId(null);
    }
  };

  const fallbackEvaluation = async (projectId: string) => {
    // Simple fallback evaluation logic
    const project = projects.find(p => p.id === projectId);
    if (!project) throw new Error('Project not found');

    // Basic evaluation criteria
    const hasTitle = project.title.trim().length > 0;
    const hasDescription = project.description.trim().length > 10;
    const titleLength = project.title.trim().length;
    const descriptionLength = project.description.trim().length;

    // Simple scoring system
    let score = 0;
    const feedback = [];
    const suggestions = [];

    if (hasTitle && titleLength >= 5) {
      score += 25;
    } else {
      suggestions.push("Provide a more descriptive title (at least 5 characters)");
    }

    if (hasDescription && descriptionLength >= 50) {
      score += 25;
    } else {
      suggestions.push("Provide a more detailed description (at least 50 characters)");
    }

    if (descriptionLength >= 100) {
      score += 25;
    } else {
      suggestions.push("Add more details about your project goals and implementation");
    }

    if (project.description.toLowerCase().includes('goal') ||
        project.description.toLowerCase().includes('objective') ||
        project.description.toLowerCase().includes('purpose')) {
      score += 25;
    } else {
      suggestions.push("Clearly state your project's goals and objectives");
    }

    const recommendation = score >= 75 ? 'APPROVE' : score >= 50 ? 'PENDING' : 'REJECT';
    const status = recommendation === 'APPROVE' ? 'approved' :
                  recommendation === 'REJECT' ? 'rejected' : 'pending';

    let feedbackText = '';
    if (recommendation === 'APPROVE') {
      feedbackText = 'Your project meets the basic criteria and has been approved. Good job on providing clear details!';
    } else if (recommendation === 'PENDING') {
      feedbackText = 'Your project shows promise but needs some improvements before approval.';
    } else {
      feedbackText = 'Your project needs significant improvements before it can be approved.';
    }

    const evaluationResult = {
      recommendation,
      feedback: feedbackText,
      suggestions
    };

    // Update project in database
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        status,
        ai_feedback: JSON.stringify(evaluationResult)
      })
      .eq('id', projectId);

    if (updateError) throw updateError;

    console.log('Used frontend fallback evaluation');
    toast({
      title: "Evaluation complete!",
      description: `Project has been ${status} (using frontend fallback evaluation)`,
    });

    await fetchProjects();
  };

  const handleEditProject = (projectId: string) => {
    setEditingId(projectId);
  };

  const handleCancelEdit = () => {
    setEditingId(null);
  };

  const handleSaveEdit = async () => {
    setEditingId(null);
    await fetchProjects();
  };

  const resetProjectForReEvaluation = async (projectId: string) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update({
          status: 'pending',
          ai_feedback: null
        })
        .eq('id', projectId);

      if (error) throw error;

      toast({
        title: "Project reset!",
        description: "Your project is now ready for re-evaluation.",
      });

      await fetchProjects();
    } catch (error: any) {
      toast({
        title: "Error",
        description: "Failed to reset project",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [user.id, refreshTrigger]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    }
  };

  const parseFeedback = (feedback: string | null) => {
    if (!feedback) return null;
    try {
      return JSON.parse(feedback);
    } catch {
      return { feedback: feedback, suggestions: [] };
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Your Projects</h2>
        <Button variant="outline" onClick={fetchProjects}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {projects.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <p className="text-muted-foreground">No projects submitted yet</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {projects.map((project) => {
            const feedback = parseFeedback(project.ai_feedback);

            // Show edit form if this project is being edited
            if (editingId === project.id) {
              return (
                <ProjectEditForm
                  key={project.id}
                  project={project}
                  onCancel={handleCancelEdit}
                  onSave={handleSaveEdit}
                />
              );
            }

            return (
              <Card key={project.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle>{project.title}</CardTitle>
                        {project.updated_at !== project.created_at && (
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                            Updated
                          </Badge>
                        )}
                      </div>
                      <CardDescription className="mt-2">
                        Submitted on {new Date(project.created_at).toLocaleDateString()}
                        {project.updated_at !== project.created_at && (
                          <span className="text-blue-600 ml-2">
                            • Last updated on {new Date(project.updated_at).toLocaleDateString()}
                          </span>
                        )}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(project.status)}>
                        {project.status.toUpperCase()}
                      </Badge>

                      {/* Action buttons based on status */}
                      <div className="flex flex-col gap-2">
                        {project.status === 'pending' && (
                          <Button
                            size="sm"
                            onClick={() => triggerEvaluation(project.id)}
                            disabled={evaluatingId === project.id}
                          >
                            {evaluatingId === project.id ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Evaluating...
                              </>
                            ) : (
                              'Evaluate with AI'
                            )}
                          </Button>
                        )}

                        {(project.status === 'rejected' || project.status === 'pending') && (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditProject(project.id)}
                              disabled={evaluatingId === project.id}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            {project.status === 'rejected' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => resetProjectForReEvaluation(project.id)}
                                disabled={evaluatingId === project.id}
                                title="Reset to pending without editing"
                              >
                                <RotateCcw className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        )}

                        {project.status === 'pending' && (
                          <p className="text-xs text-muted-foreground text-center">
                            Click to get AI feedback
                          </p>
                        )}

                        {project.status === 'rejected' && (
                          <p className="text-xs text-red-600 text-center">
                            Edit to improve & re-evaluate
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Description</h4>
                    <p className="text-sm text-muted-foreground">{project.description}</p>
                  </div>
                  
                  {feedback && (
                    <div>
                      <h4 className="font-medium mb-2">AI Feedback</h4>
                      <p className="text-sm text-muted-foreground mb-3">{feedback.feedback}</p>

                      {feedback.suggestions && feedback.suggestions.length > 0 && (
                        <div>
                          <h5 className="font-medium text-sm mb-2">Suggestions for Improvement:</h5>
                          <ul className="text-sm text-muted-foreground space-y-1">
                            {feedback.suggestions.map((suggestion: string, index: number) => (
                              <li key={index} className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>{suggestion}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {project.status === 'rejected' && (
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                          <p className="text-sm text-red-800 font-medium mb-2">
                            🔄 Ready to improve your project?
                          </p>
                          <p className="text-xs text-red-700">
                            Click "Edit" to update your project based on the feedback above,
                            or use the reset button to re-evaluate without changes.
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}