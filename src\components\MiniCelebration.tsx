import { useEffect, useState } from 'react';
import './confetti.css';

interface MiniCelebrationProps {
  isActive: boolean;
  onComplete?: () => void;
  duration?: number;
}

export function MiniCelebration({ isActive, onComplete, duration = 2000 }: MiniCelebrationProps) {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (!isActive) return;

    setIsAnimating(true);

    const cleanup = setTimeout(() => {
      setIsAnimating(false);
      onComplete?.();
    }, duration);

    return () => {
      clearTimeout(cleanup);
    };
  }, [isActive, duration, onComplete]);

  if (!isAnimating) return null;

  // Generate small confetti pieces
  const confettiPieces = Array.from({ length: 12 }, (_, i) => {
    const delay = Math.random() * 300;
    const duration = 1500 + Math.random() * 500;
    const startX = 20 + Math.random() * 60; // Keep within small area
    const endX = startX + (Math.random() - 0.5) * 40;
    const rotation = Math.random() * 360;
    const scale = 0.4 + Math.random() * 0.4; // Smaller size
    
    return (
      <div
        key={i}
        className="absolute text-sm pointer-events-none"
        style={{
          left: `${startX}%`,
          top: '0%',
          fontSize: `${scale}em`,
          animationDelay: `${delay}ms`,
          animationDuration: `${duration}ms`,
          transform: `rotate(${rotation}deg)`,
        }}
      >
        <div className="animate-mini-confetti-fall">
          {['🎉', '🎊', '✨', '⭐'][Math.floor(Math.random() * 4)]}
        </div>
      </div>
    );
  });

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-lg">
      {confettiPieces}
      
      {/* Small celebration message */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center animate-mini-bounce">
          <div className="text-2xl mb-1">🎉</div>
          <div className="bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg">
            Approved!
          </div>
        </div>
      </div>
      
      {/* Subtle background effect */}
      <div className="absolute inset-0 bg-emerald-100/30 rounded-lg animate-pulse" />
    </div>
  );
}
